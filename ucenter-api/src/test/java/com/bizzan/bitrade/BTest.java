package com.bizzan.bitrade;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.FileOutputStream;
import java.util.Random;

public class BTest {

    private static String text = "0123456789abcdefghijklmnopqrstuvwxyz";
    private static int length = 4;
    private static int width = 200;
    private static int height = 64;
    private static boolean crossLine = false;
    private static boolean twistImage = true;

    private static Font font = new Font("Monospaced", Font.ITALIC | Font.BOLD, (int)(height*0.8));

    public static void main(String[] args) throws Exception {

        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        // 获取图形上下文
        Graphics g = image.getGraphics();
        // 生成随机类
        Random random = new Random();
        // 设定背景色
        g.setColor(new Color(255, 255, 255));
        g.fillRect(0, 0, width, height);
        // 设定字体
        g.setFont(font);
        // 取随机产生的验证码
        String sRand = "";
        //文字X坐标
        int codeX = width/(length+1);
        for (int i = 0; i < length; i++) {
            String rand = String.valueOf(getRandomText());
            sRand += rand;
            //将验证码输出到图象中
            g.setColor(getRandColor(100, 150));// 调用函数出来的颜色相同
            g.drawString(rand, codeX * i + 10, height-20);
        }
        // 生成干扰线
        if (crossLine) {
            for (int i = 0; i < (random.nextInt(5) + 5); i++) {
                g.setColor(new Color(random.nextInt(255) + 1, random.nextInt(255) + 1, random.nextInt(255) + 1));
                g.drawLine(random.nextInt(width), random.nextInt(height), random.nextInt(width),
                        random.nextInt(height));
            }
        }
        if(twistImage){
            image = twistImage(image);
        }
//        String pageId = request(request,"cid");
//        String key = "CAPTCHA_" + pageId;
        // 将验证码存入页面KEY值的SESSION里面
//        HttpSession session = request.getSession();
//        session.setAttribute(key, sRand);
//        session.setAttribute(key + "_time", System.currentTimeMillis());
//		redisUtil.set(key, sRand);

        // 图象生效
        g.dispose();
//        ServletOutputStream responseOutputStream = response.getOutputStream();
        FileOutputStream responseOutputStream = new FileOutputStream("a.jpg");
        // 输出图象到页面
        ImageIO.write(image, "JPEG", responseOutputStream);

    }

    static char getRandomText() {
        Random random = new Random();
        return text.charAt(random.nextInt(text.length()));
    }

    static Color getRandColor(int fc, int bc) {
        Random random = new Random();
        if (fc > 255) {
            fc = 255;
        }
        if (bc > 255) {
            bc = 255;
        }
        int r = fc + random.nextInt(bc - fc);
        int g = fc + random.nextInt(bc - fc);
        int b = fc + random.nextInt(bc - fc);
        return new Color(r, g, b);
    }

    static BufferedImage twistImage(BufferedImage buffImg) {
        Random random = new Random();
        double dMultValue = random.nextInt(10) + 5;// 波形的幅度倍数，越大扭曲的程序越高，一般为3
        double dPhase = random.nextInt(6);// 波形的起始相位，取值区间(0-2＊PI)
        BufferedImage destBi = new BufferedImage(buffImg.getWidth(),
                buffImg.getHeight(), BufferedImage.TYPE_INT_RGB);
        Graphics g = destBi.getGraphics();
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, buffImg.getWidth(), buffImg.getHeight());
        for (int i = 0; i < destBi.getWidth(); i++) {
            for (int j = 0; j < destBi.getHeight(); j++) {
                int nOldX = getXPosition4Twist(dPhase, dMultValue,destBi.getHeight(), i, j);
                int nOldY = j;
                if (nOldX >= 0 && nOldX < destBi.getWidth() && nOldY >= 0
                        && nOldY < destBi.getHeight()) {
                    destBi.setRGB(nOldX, nOldY, buffImg.getRGB(i, j));
                }
            }
        }
        return destBi;
    }

    static int getXPosition4Twist(double dPhase, double dMultValue,
                                   int height, int xPosition, int yPosition) {
        double PI = 3.*******************************; // 此值越大，扭曲程度越大
        double dx = (double) (PI * yPosition) / height + dPhase;
        double dy = Math.sin(dx);
        return xPosition + (int) (dy * dMultValue);
    }

}
