package com.bizzan.bitrade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bizzan.bitrade.controller.WalletUserController;
import com.bizzan.bitrade.entity.CoinThumb;
import com.bizzan.bitrade.entity.Member;
import com.bizzan.bitrade.service.EthService;
import com.bizzan.bitrade.util.JSONUtils;
import com.bizzan.bitrade.util.Md5;
import com.bizzan.bitrade.util.MessageResult;
import com.sparkframework.security.Encrypt;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.bouncycastle.util.encoders.Hex;
import org.junit.Test;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisShardInfo;

import java.io.File;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.Signature;
import java.util.*;
import java.util.concurrent.Future;

import org.web3j.crypto.*;
import org.web3j.utils.Numeric;

public class ATest {

    public static void main(String[] args) throws Exception {

//       testKafka();

//        testPwd();

//        String id = "market.qoeusdt.detail";
//
//        StringBuilder sb = new StringBuilder(id.split("\\.")[1]);
//        String symbol = sb.insert(sb.indexOf("usdt"), "/").toString().toUpperCase();
//
//        String type = id.split("\\.")[2];
//
//        System.out.println(symbol);
//        System.out.println(type);

        System.setProperty("user.timezone", "America/New_York");


        System.out.println(new Date());

//        testDeposits();

//        testPrice();

//        testRedis();

    }

    @Test
    public void testValidSign() throws Exception {
        String address = "******************************************";
        String message = "hello";
        String signature = "0xbfb56cb5e4426bf1a54eeb09d0d78eab84948afb5f8d950f9e7ede3a911ce5f839a05a07e097f56a7235d6149aceca55809465cb00f36f9c63b480cad104a6c51b"; // 替换为您的签名

        boolean isValid = EthService.validSign(address, message, signature);

        if (isValid) {
            System.out.println("Signature is valid.");
        } else {
            System.out.println("Signature is not valid.");
        }
    }

    @Test
    public void testUserPwd() throws Exception {
        String address = "******************************************";
        String password = address.toLowerCase().substring(30);
        Member member = new Member();
        member.setSalt("373331323332303533333537353830323838");
        System.out.println(Md5.md5Digest(password + member.getSalt()).toLowerCase());
        System.out.println(Md5.md5Digest(password + member.getSalt()).toLowerCase().equals("a287e41ac6cc8a86456462fc164b180e"));
//        password = a287e41ac6cc8a86456462fc164b180e
    }

    @Test
    public void testSign() throws Exception {
//        String s = WalletUtils.generateNewWalletFile("", new File("/Users/<USER>/block2023/bizzzans_contract/ztuo-framework/logs/keystore"), true);
//        System.out.println(s);
//        WalletUtils.generateNewWalletFile()

        Credentials c = WalletUtils.loadCredentials("", new File("/Users/<USER>/block2023/bizzzans_contract/ztuo-framework/logs/keystore/UTC--2023-07-01T08-47-14.269000000Z--a77c6ad976e43d669af3447c49533da8f05bd8ba.json"));
        ECKeyPair keyPair = c.getEcKeyPair();

        String msg = WalletUserController.getLoginMsg();

        Sign.SignatureData signatureData = Sign.signPrefixedMessage(msg.getBytes(StandardCharsets.UTF_8), keyPair);

        String sign = "0x" + Hex.toHexString(signatureData.getR()) + Hex.toHexString(signatureData.getS()) + Hex.toHexString(signatureData.getV());

        System.out.println("address = " + c.getAddress());
        System.out.println("msg = " + msg);
        System.out.println("sign = " + sign);
        System.out.println("valid =" + EthService.validSign(c.getAddress(), msg, sign));

//        curl -X POST -H 'lang: en_US' "http://**************/uc/register/wallet?address=******************************************&sign=0xe1e9f4e3d137d8c89fe8ec2cabb5f516e8472f91a7c58e7dd25547d0c1beb7e91611d86f4260ab1def297632e0e5d24e1c3d6e79fc51137e6476b2377e194a4c1b"
//        curl -X POST -H 'lang: en_US' "http://**************/uc/register/wallet?address=******************************************&sign=0x4825aab4f0d7cde6457ac39f744c35af067d760c7309869139c04c5f9af20d8b337a71350ee543472dc05003e0cd6c30caf82858431a47bd5d50042683194bf91c"
//        curl -X POST -H 'lang: en_US' "http://**************/uc/login/wallet?address=******************************************&sign=0x4825aab4f0d7cde6457ac39f744c35af067d760c7309869139c04c5f9af20d8b337a71350ee543472dc05003e0cd6c30caf82858431a47bd5d50042683194bf91c"

    }

    static void testRedis() {
        RedisTemplate redisTemplate = new RedisTemplate();
        JedisConnectionFactory factory = new JedisConnectionFactory();
        JedisShardInfo info = new JedisShardInfo("127.0.0.1", 6379);
        info.setPassword("0669");
//        factory.setHostName("127.0.0.1");
//        factory.setPort(6379);
//        factory.setPassword("0669");
        factory.setShardInfo(info);

        redisTemplate.setDefaultSerializer(new StringRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.afterPropertiesSet();

        String token = "kk123";
        Object val = redisTemplate.opsForHash().get("api:token", token);
        System.out.println("val = " + val);
        if (val == null) {
            System.out.println("ERR");
        }
    }

    static void testPrice() {

//        String id = "market.BTC-USDT.detail";
        String id = "market.Testusdt.detail";
        StringBuilder sb = new StringBuilder(id.split("\\.")[1]);
        System.out.println(sb);
        String symbol = sb.insert(sb.indexOf("usdt"), "/").toString().toUpperCase();
        System.out.println(symbol);
        String type = id.split("\\.")[2];
        System.out.println(type);


//        BigDecimal entrustPrice = new BigDecimal("0.01");
//        BigDecimal currentPrice = BigDecimal.ZERO;
//        // 委托价格是否太高或太低(限价单需要在2%的价格范围内下单)
//        if (entrustPrice.compareTo(currentPrice.multiply(BigDecimal.ONE.add(BigDecimal.valueOf(0.2)))) > 0
//                || entrustPrice.compareTo(currentPrice.multiply(BigDecimal.ONE.subtract(BigDecimal.valueOf(0.2)))) < 0) {
//            System.out.println("ORDER_PRICE_OVERRUN");
//        } else {
//            System.out.println("OK");
//        }
    }

    static void testPwd() {
        String password = "adminasd233!";
        String md5Key = "XehGyeyrVgOV4P8Uf70REVpIw3iVNwNs";
        System.out.println(Encrypt.MD5(password + md5Key));
//        30e0c36f966ae204287d8e8e4f18bbad
    }

    static void testDeposits() throws Exception {
        // 设置 Kafka 生产者的配置
        Properties props = kafkaProps();
        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        Map<String, Object> map = new HashMap<>();
        map.put("address", "0x123");
        map.put("amount", "10000000");
        map.put("txid", "0x0000001");

        Map<String, Object> map2 = new HashMap<>();
        map2.put("address", "0x456");
        map2.put("amount", "1000");
        map2.put("txid", "0x0000002");

        System.out.println(JSON.toJSONString(map));
        System.out.println(JSON.toJSONString(map2));

//        admin@ip-172-31-13-210:/opt/kafka$ ./bin/kafka-console-producer.sh --broker-list=127.0.0.1:9092 --topic=deposit --property "parse.key=true" --property "key.separator=:"
//        >USDT:{"amount":"10000000","address":"0x123","txid":"0x0000001"}
//        >ETH:{"amount":"1000","address":"0x456","txid":"0x0000002"}


//        System.out.println(producer.send(new ProducerRecord<>("deposit", "USDT", JSON.toJSONString(map))).get());
//        System.out.println(producer.send(new ProducerRecord<>("deposit", "Ethereum", JSON.toJSONString(map2))).get());
    }

    static void testKafka() throws Exception {
        // 设置 Kafka 生产者的配置
        Properties props = kafkaProps();

        KafkaConsumer<String, String> consumer = new KafkaConsumer<>(props);

        Map<String, List<PartitionInfo>> topics = consumer.listTopics();

        topics.forEach((k, v) -> {
            System.out.println(k + " => " + v);
        });

        System.out.println("=======");

        KafkaProducer<String, String> producer = new KafkaProducer<>(props);

        // 注册初始化钱包
        Future<RecordMetadata> future = producer.send(new ProducerRecord<>("member-register", "{\"uid\":7383}"));

        System.out.println(future.get());
//        producer.send(new ProducerRecord<>("member-register-swap", "{}"));
    }

    static Properties kafkaProps() {
        Properties props = new Properties();
//        props.put("bootstrap.servers", "localkafka:9092");
        props.put("bootstrap.servers", "127.0.0.1:9092");
        props.put("acks", "all");
        props.put("key.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("value.serializer", "org.apache.kafka.common.serialization.StringSerializer");
        props.put("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");
        props.put("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer");

        return props;
    }

    @Test
    public void testScale() throws Exception {
        System.out.println(scaleAmount(10));
        System.out.println(scaleAmount(20));
        System.out.println(scaleAmount(30));

        System.out.println(this.rand.nextDouble());
        System.out.println(this.rand.nextDouble());
        System.out.println(this.rand.nextInt(6));
        System.out.println(this.rand.nextInt(6));
    }

    protected Random rand = new Random();

    protected BigDecimal scaleAmount(double temRand) {
        BigDecimal randAmount = BigDecimal.valueOf(0);
        while(randAmount.compareTo(BigDecimal.ZERO) == 0) {
            randAmount = BigDecimal.valueOf(this.rand.nextDouble()*temRand + 0.001).setScale(this.rand.nextInt(6)+1, BigDecimal.ROUND_HALF_DOWN);
        }
        return randAmount;
    }

}
