<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<contextName>logback</contextName>
	<!--输出到控制台 -->
	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%d{HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n </pattern>
		</encoder>
	</appender>

	<!--按天生成日志 -->
	<appender name="logFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<FileNamePattern> @logback.path@/contract-option-api/%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.log </FileNamePattern>
			<maxFileSize>200MB</maxFileSize>
			<maxHistory>100</maxHistory>
		</rollingPolicy>
		<encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
		</encoder>
	</appender>

	<root level="info">
		<appender-ref ref="console" />
		<appender-ref ref="logFile" />
	</root>
</configuration>